const express = require('express');
const path = require('path');
const fs = require('fs');
const cors = require('cors');
const { scrapeAITools } = require('./scraper');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// API Routes

// Get all AI tools
app.get('/api/tools', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'data', 'ai-tools.json');
        
        if (!fs.existsSync(dataPath)) {
            return res.status(404).json({ 
                error: 'AI tools data not found. Please run the scraper first.' 
            });
        }
        
        const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        res.json(data);
    } catch (error) {
        console.error('Error reading AI tools data:', error);
        res.status(500).json({ error: 'Failed to load AI tools data' });
    }
});

// Get tools by category
app.get('/api/tools/category/:category', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'data', 'ai-tools.json');
        
        if (!fs.existsSync(dataPath)) {
            return res.status(404).json({ 
                error: 'AI tools data not found. Please run the scraper first.' 
            });
        }
        
        const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        const category = req.params.category;
        const filteredTools = data.tools.filter(tool => 
            tool.category.toLowerCase() === category.toLowerCase()
        );
        
        res.json({
            category: category,
            tools: filteredTools,
            count: filteredTools.length
        });
    } catch (error) {
        console.error('Error filtering tools by category:', error);
        res.status(500).json({ error: 'Failed to filter tools by category' });
    }
});

// Trigger scraping
app.post('/api/scrape', async (req, res) => {
    try {
        console.log('Starting AI tools scraping...');
        const data = await scrapeAITools();
        res.json({ 
            success: true, 
            message: `Successfully scraped ${data.totalTools} AI tools`,
            data: data
        });
    } catch (error) {
        console.error('Scraping failed:', error);
        res.status(500).json({ 
            success: false, 
            error: 'Failed to scrape AI tools',
            message: error.message 
        });
    }
});

// Get categories
app.get('/api/categories', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'data', 'ai-tools.json');
        
        if (!fs.existsSync(dataPath)) {
            return res.status(404).json({ 
                error: 'AI tools data not found. Please run the scraper first.' 
            });
        }
        
        const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        res.json({
            categories: data.categories,
            count: data.categories.length
        });
    } catch (error) {
        console.error('Error getting categories:', error);
        res.status(500).json({ error: 'Failed to get categories' });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        service: 'AI Tools Website'
    });
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Handle 404 for API routes
app.use('/api/*', (req, res) => {
    res.status(404).json({ error: 'API endpoint not found' });
});

// Handle 404 for all other routes
app.use('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({ 
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 AI Tools Website server running on http://localhost:${PORT}`);
    console.log(`📊 API available at http://localhost:${PORT}/api/tools`);
    console.log(`🔧 Health check at http://localhost:${PORT}/api/health`);
    
    // Check if data exists, if not suggest running scraper
    const dataPath = path.join(__dirname, 'data', 'ai-tools.json');
    if (!fs.existsSync(dataPath)) {
        console.log(`⚠️  No AI tools data found. Run 'npm run scrape' to generate data.`);
    } else {
        console.log(`✅ AI tools data loaded successfully.`);
    }
});

module.exports = app;
