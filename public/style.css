/* Custom CSS for AI Tools Website - Netflix Theme */

:root {
    --primary-color: #e50914; /* Netflix Red */
    --secondary-color: #564d4d;
    --success-color: #46d369;
    --info-color: #0dcaf0;
    --warning-color: #ffa00a;
    --danger-color: #b81d24;
    --light-color: #f5f5f1;
    --dark-color: #141414; /* Netflix Dark */
    --background-color: #000000;
    --text-color: #ffffff;
    --card-bg: #181818;
    --hover-bg: #232323;
    --gradient-bg: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --card-hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    --border-radius: 4px;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Netflix Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* Navigation */
.navbar {
    background-color: var(--background-color);
    padding: 1rem 4%;
    transition: background-color 0.3s;
}

.navbar.scrolled {
    background-color: var(--background-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color) !important;
}

/* Filter Section */
.filter-section {
    background-color: var(--dark-color);
    border-bottom: 1px solid #333;
    padding: 1.5rem 0;
}

.filter-section h5 {
    color: var(--text-color);
}

/* Hero Section */
.hero-section {
    position: relative;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    padding: 4rem 0;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 50%, rgba(229, 9, 20, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(229, 9, 20, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(229, 9, 20, 0.08) 0%, transparent 50%);
    animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.min-vh-50 {
    min-height: 50vh;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #e50914 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #cccccc;
    margin-bottom: 2rem;
    line-height: 1.6;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #999;
    margin-top: 0.5rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-cards {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-cards::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(229, 9, 20, 0.3) 20%, rgba(229, 9, 20, 0.6) 50%, rgba(229, 9, 20, 0.3) 80%, transparent 100%);
    z-index: 0;
}

.floating-card {
    position: absolute;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    border: 1px solid rgba(229, 9, 20, 0.2);
}

.floating-card img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.floating-card span {
    display: block;
    color: var(--text-color);
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
}

.card-1 {
    top: 50%;
    left: 25%;
    transform: translateY(-50%);
    width: 140px;
    animation: float1 6s ease-in-out infinite;
    z-index: 3;
}

.card-2 {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 140px;
    animation: float2 8s ease-in-out infinite;
    z-index: 2;
}

.card-3 {
    top: 50%;
    right: 25%;
    transform: translateY(-50%);
    width: 140px;
    animation: float3 7s ease-in-out infinite;
    z-index: 1;
}

@keyframes float1 {
    0%, 100% { transform: translateY(-50%) translateX(0px) rotate(0deg); }
    50% { transform: translateY(-50%) translateX(-10px) rotate(2deg); }
}

@keyframes float2 {
    0%, 100% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg); }
    50% { transform: translate(-50%, -50%) translateY(-15px) rotate(-2deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(-50%) translateX(0px) rotate(0deg); }
    50% { transform: translateY(-50%) translateX(10px) rotate(1deg); }
}

.card-1:hover {
    transform: translateY(-50%) scale(1.05) translateY(-55px);
}

.card-2:hover {
    transform: translate(-50%, -50%) scale(1.05) translateY(-15px);
}

.card-3:hover {
    transform: translateY(-50%) scale(1.05) translateY(-55px);
}



/* Tool Cards Grid */
.tools-section {
    padding: 2rem 0;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    align-items: stretch;
}

/* Responsive grid adjustments */
@media (min-width: 1400px) {
    .tools-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 992px) and (max-width: 1399px) {
    .tools-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.tool-card {
    background: var(--card-bg);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    height: 100%;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    color: var(--text-color);
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

/* Card Image Container */
.tool-card-img-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    overflow: hidden;
    background-color: var(--dark-color);
}

.tool-card-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.tool-card:hover .tool-card-img {
    transform: scale(1.05);
}

/* Card Content */
.tool-card .card-body {
    padding: 1.5rem;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 100%);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.tool-card .card-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

.tool-card .card-text {
    color: #999;
    line-height: 1.5;
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.tool-category {
    background: var(--primary-color);
    color: var(--text-color);
    padding: 0.25rem 0.75rem;
    border-radius: 2px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
    display: inline-block;
}



@media (max-width: 576px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .floating-card {
        width: 120px !important;
    }

    .floating-card img {
        height: 60px;
    }

    .hero-visual {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-visual {
        margin-top: 2rem;
    }

    .card-1 {
        top: 50%;
        left: 10%;
        transform: translateY(-50%);
    }

    .card-2 {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .card-3 {
        top: 50%;
        right: 10%;
        transform: translateY(-50%);
    }
}

/* Responsive Design */
@media (max-width: 1400px) {
    .container {
        max-width: 95%;
    }
}

@media (max-width: 1200px) {
    .tool-card .card-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 767px) {
    .tool-card-img-container {
        padding-top: 75%; /* 4:3 Aspect Ratio for mobile */
    }
}

/* Hover Effects */
.tool-card:hover {
    transform: scale(1.03);
    box-shadow: var(--card-hover-shadow);
    background: var(--hover-bg);
}

.tool-card:hover .card-title {
    color: var(--primary-color);
}

/* Loading Animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--primary-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    background-color: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff0f1f;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 2px;
}

.btn-primary:hover {
    background-color: #ff0f1f;
    border-color: #ff0f1f;
    transform: scale(1.05);
}

/* Form Elements */
.form-control, .form-select {
    background-color: var(--card-bg);
    border: 1px solid #333;
    color: var(--text-color);
    border-radius: 2px;
}

.form-control:focus, .form-select:focus {
    background-color: var(--card-bg);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
}

/* Animation Refinements */
.tool-card {
    animation: fadeInUp 0.6s ease-out;
    will-change: transform;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* Stagger animation with shorter delays */
.tool-card:nth-child(1) { animation-delay: 0.1s; }
.tool-card:nth-child(2) { animation-delay: 0.15s; }
.tool-card:nth-child(3) { animation-delay: 0.2s; }
.tool-card:nth-child(4) { animation-delay: 0.25s; }
