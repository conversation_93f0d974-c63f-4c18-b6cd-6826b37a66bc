<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Web Library</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="style.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'></text></svg>">
</head>
<body class="bg-dark">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="#">
                <i class="bi bi-robot me-2"></i>
                AI Web Library
            </a>

        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background"></div>
        <div class="container-fluid">
            <div class="row align-items-center min-vh-50">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            Discover the Future of AI
                        </h1>
                        <p class="hero-subtitle">
                            Explore 55+ cutting-edge AI tools across 25 categories. From ChatGPT to Midjourney, find the perfect AI solution for your needs.
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <span class="stat-number">55+</span>
                                <span class="stat-label">AI Tools</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">25+</span>
                                <span class="stat-label">Categories</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">2025</span>
                                <span class="stat-label">Latest</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual">
                        <div class="floating-cards">
                            <div class="floating-card card-1">
                                <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=200&h=120&fit=crop&crop=center&auto=format&q=80" alt="AI Chat">
                                <span>ChatGPT</span>
                            </div>
                            <div class="floating-card card-2">
                                <img src="https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=200&h=120&fit=crop&crop=center&auto=format&q=80" alt="AI Art">
                                <span>Midjourney</span>
                            </div>
                            <div class="floating-card card-3">
                                <img src="https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=200&h=120&fit=crop&crop=center&auto=format&q=80" alt="AI Video">
                                <span>Synthesia</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-3 mb-md-0">
                        <i class="bi bi-funnel me-2"></i>
                        Browse All Tools
                    </h5>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-3">
                        <select id="categoryFilter" class="form-select">
                            <option value="">All Categories</option>
                        </select>
                        <button id="refreshBtn" class="btn btn-primary" title="Refresh Data">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Grid Section -->
    <section class="tools-section">
        <div class="container-fluid">
            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="text-center py-5">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-light">Loading AI tools...</p>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="alert alert-danger d-none" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <span id="errorText">Failed to load AI tools. Please try again.</span>
                <button id="retryBtn" class="btn btn-sm btn-outline-danger ms-3">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
            </div>

            <!-- Tools Grid -->
            <div id="toolsGrid" class="tools-grid">
                <!-- Tools will be dynamically loaded here -->
            </div>

            <!-- No Results Message -->
            <div id="noResults" class="text-center py-5 d-none">
                <i class="bi bi-search display-1 text-muted"></i>
                <h4 class="mt-3 text-light">No tools found</h4>
                <p class="text-muted">Try selecting a different category or clear the filter.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 mt-5" style="background-color: var(--dark-color);">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0 text-muted">
                        © 2025 AiwebLibrary. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Card Template -->
    <template id="toolCardTemplate">
        <div class="tool-card">
            <div class="tool-card-img-container">
                <img class="tool-card-img" src="" alt="" loading="lazy">
            </div>
            <div class="card-body">
                <span class="tool-category"></span>
                <h5 class="card-title"></h5>
                <p class="card-text"></p>
            </div>
        </div>
    </template>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>
