// AI Tools Website Frontend JavaScript

class AIToolsApp {
    constructor() {
        this.tools = [];
        this.categories = [];
        this.filteredTools = [];
        this.currentCategory = '';
        this.cardTemplate = document.getElementById('toolCardTemplate');


        this.init();
    }

    async init() {
        this.bindEvents();
        await this.loadData();
        this.setupScrollListener();
    }

    setupScrollListener() {
        let navbar = document.querySelector('.navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    bindEvents() {
        // Category filter change
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filterByCategory(e.target.value);
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadData(true);
        });

        // Retry button
        document.getElementById('retryBtn').addEventListener('click', () => {
            this.loadData(true);
        });


    }

    async loadData(forceRefresh = false) {
        try {
            this.showLoading();
            this.hideError();

            const response = await fetch('/api/tools');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            this.tools = data.tools || [];
            this.categories = data.categories || [];
            this.filteredTools = [...this.tools];

            this.populateCategories();
            this.renderTools();
            this.updateStats();
            this.hideLoading();

            console.log(`Loaded ${this.tools.length} AI tools across ${this.categories.length} categories`);

        } catch (error) {
            console.error('Error loading data:', error);
            this.showError(error.message);
            this.hideLoading();
        }
    }

    populateCategories() {
        const categorySelect = document.getElementById('categoryFilter');

        // Clear existing options except "All Categories"
        categorySelect.innerHTML = '<option value="">All Categories</option>';

        // Add category options
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categorySelect.appendChild(option);
        });
    }

    filterByCategory(category) {
        this.currentCategory = category;

        if (category === '') {
            this.filteredTools = [...this.tools];
        } else {
            this.filteredTools = this.tools.filter(tool =>
                tool.category === category
            );
        }

        this.renderTools();
        this.updateFilterStats();
    }

    renderTools() {
        const toolsGrid = document.getElementById('toolsGrid');
        const noResults = document.getElementById('noResults');

        if (this.filteredTools.length === 0) {
            toolsGrid.innerHTML = '';
            noResults.classList.remove('d-none');
            return;
        }

        noResults.classList.add('d-none');
        toolsGrid.innerHTML = '';

        this.filteredTools.forEach(tool => {
            const card = this.createToolCard(tool);
            toolsGrid.appendChild(card);
        });

        // Add click events to tool cards
        this.addToolCardEvents();
    }

    createToolCard(tool) {
        const cardClone = this.cardTemplate.content.cloneNode(true);
        const card = cardClone.querySelector('.tool-card');
        const img = cardClone.querySelector('.tool-card-img');
        const category = cardClone.querySelector('.tool-category');
        const title = cardClone.querySelector('.card-title');
        const description = cardClone.querySelector('.card-text');

        // Set card data
        card.dataset.url = tool.url;
        img.src = tool.imageUrl;
        img.alt = `${tool.name} logo`;
        category.textContent = tool.category;
        title.textContent = tool.name;
        description.textContent = tool.description;

        return cardClone;
    }

    addToolCardEvents() {
        const toolCards = document.querySelectorAll('.tool-card');

        toolCards.forEach(card => {
            // Click event
            card.addEventListener('click', () => {
                const url = card.dataset.url;
                if (url && url !== '#') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                }
            });

            // Keyboard event for accessibility
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
            });

            // Add hover effect for better UX
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    updateStats() {
        // Stats updated - tool count removed from header
    }

    updateFilterStats() {
        const toolCount = document.getElementById('toolCount');
        if (this.currentCategory) {
            toolCount.textContent = `${this.filteredTools.length} tools in ${this.currentCategory}`;
        } else {
            toolCount.textContent = `${this.tools.length} AI Tools`;
        }
    }

    showLoading() {
        document.getElementById('loadingSpinner').classList.remove('d-none');
        document.getElementById('toolsGrid').innerHTML = '';
        document.getElementById('noResults').classList.add('d-none');
    }

    hideLoading() {
        document.getElementById('loadingSpinner').classList.add('d-none');
    }

    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');

        errorText.textContent = message || 'Failed to load AI tools. Please try again.';
        errorMessage.classList.remove('d-none');
    }

    hideError() {
        document.getElementById('errorMessage').classList.add('d-none');
    }



    // Utility method to search tools (for future enhancement)
    searchTools(query) {
        if (!query) {
            this.filteredTools = [...this.tools];
        } else {
            this.filteredTools = this.tools.filter(tool =>
                tool.name.toLowerCase().includes(query.toLowerCase()) ||
                tool.description.toLowerCase().includes(query.toLowerCase()) ||
                tool.category.toLowerCase().includes(query.toLowerCase())
            );
        }
        this.renderTools();
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.aiToolsApp = new AIToolsApp();
});

// Add some utility functions for potential future enhancements
window.aiToolsUtils = {
    // Copy tool URL to clipboard
    copyToolUrl: (url) => {
        navigator.clipboard.writeText(url).then(() => {
            console.log('URL copied to clipboard');
        });
    },

    // Share tool
    shareTool: (tool) => {
        if (navigator.share) {
            navigator.share({
                title: tool.name,
                text: tool.description,
                url: tool.url
            });
        }
    },

    // Get tools by category
    getToolsByCategory: (category) => {
        return window.aiToolsApp.tools.filter(tool => tool.category === category);
    }
};
