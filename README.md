# AI Tools Website

A Node.js Express application that scrapes AI tools from Synthesia's "The 55 Best AI Tools for 2025" article and displays them in a responsive, filterable website.

## Features

- 🤖 **Web Scraping**: Automatically extracts AI tools data from <PERSON>ynthesia's article
- 🎨 **Responsive Design**: Bootstrap-powered responsive layout that works on all devices
- 🔍 **Category Filtering**: Filter tools by 25+ different categories
- 🚀 **Single Page Application**: Fast, smooth user experience
- 🔗 **External Links**: Click on any tool tile to visit the official website
- 📱 **Mobile Friendly**: Optimized for mobile and tablet viewing
- ⚡ **Fast Loading**: Efficient data loading and caching

## Tech Stack

- **Backend**: Node.js, Express.js
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Bootstrap 5, Custom CSS
- **Web Scraping**: Axios, Cheerio
- **Data Storage**: JSON files

## Project Structure

```
ai_web_library/
├── package.json          # Project dependencies and scripts
├── server.js             # Express server and API routes
├── scraper.js            # Web scraping logic
├── README.md             # Project documentation
├── data/
│   └── ai-tools.json     # Generated AI tools data
└── public/
    ├── index.html        # Main HTML page
    ├── style.css         # Custom CSS styles
    └── script.js         # Frontend JavaScript
```

## Installation

1. **Clone or navigate to the project directory**:
   ```bash
   cd ai_web_library
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run the scraper to generate data**:
   ```bash
   npm run scrape
   ```

4. **Start the server**:
   ```bash
   npm start
   ```

5. **Open your browser and visit**:
   ```
   http://localhost:3000
   ```

## Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start the development server with nodemon
- `npm run scrape` - Run the web scraper to update AI tools data

## API Endpoints

- `GET /` - Serve the main website
- `GET /api/tools` - Get all AI tools data
- `GET /api/tools/category/:category` - Get tools by specific category
- `GET /api/categories` - Get all available categories
- `POST /api/scrape` - Trigger data scraping
- `GET /api/health` - Health check endpoint

## Features Overview

### Web Scraping
The application scrapes data from Synthesia's AI tools article and structures it into a JSON format with:
- Tool name and description
- Category classification
- Official website URLs
- Pricing information
- Featured tool indicators

### Responsive Design
- **Desktop**: Multi-column grid layout with hover effects
- **Tablet**: Responsive grid that adapts to screen size
- **Mobile**: Single-column layout optimized for touch

### Category Filtering
Filter tools by categories including:
- AI Assistants (ChatGPT, Claude, Gemini, etc.)
- Video Generation (Synthesia, Runway, etc.)
- Image Generation (Midjourney, DALL-E, etc.)
- Writing Tools (Grammarly, Rytr, etc.)
- And 20+ more categories

### User Experience
- **Loading States**: Smooth loading animations
- **Error Handling**: Graceful error messages with retry options
- **Accessibility**: Keyboard navigation and screen reader support
- **Performance**: Optimized for fast loading and smooth interactions

## Customization

### Adding New Categories
Edit the `categories` object in `scraper.js` to add new tool categories.

### Styling
Modify `public/style.css` to customize the appearance:
- Colors and themes
- Card layouts
- Animations and transitions

### Data Structure
The AI tools data follows this structure:
```json
{
  "id": 1,
  "name": "Tool Name",
  "description": "Tool description",
  "category": "Category Name",
  "url": "https://tool-website.com",
  "pricing": "Freemium",
  "featured": false
}
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use this project for personal or commercial purposes.

## Acknowledgments

- Data sourced from [Synthesia's AI Tools Guide](https://www.synthesia.io/post/ai-tools)
- Built with Bootstrap 5 for responsive design
- Icons from Bootstrap Icons

## Troubleshooting

### Common Issues

1. **"AI tools data not found" error**:
   - Run `npm run scrape` to generate the data file

2. **Port already in use**:
   - Change the PORT in server.js or kill the process using port 3000

3. **Scraping fails**:
   - Check your internet connection
   - The target website might have changed structure

4. **Styling issues**:
   - Clear browser cache
   - Check if Bootstrap CSS is loading properly

### Development Tips

- Use `npm run dev` for development with auto-restart
- Check browser console for JavaScript errors
- Use browser dev tools to debug responsive design
- Test the API endpoints directly using tools like Postman

## Future Enhancements

Potential features to add:
- Search functionality
- Tool favorites/bookmarking
- User reviews and ratings
- Tool comparison feature
- Dark mode toggle
- Export functionality
