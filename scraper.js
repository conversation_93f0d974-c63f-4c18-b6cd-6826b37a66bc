const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');

async function scrapeAITools() {
    try {
        console.log('Starting to scrape AI tools from Synthesia...');

        const response = await axios.get('https://www.synthesia.io/post/ai-tools', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        const $ = cheerio.load(response.data);
        const aiTools = [];

        // Define categories and their tools based on the scraped content
        const categories = {
            'AI Assistants': ['ChatGPT', 'Claude', 'Gemini', 'DeepSeek', 'Grok'],
            'Video Generation': ['Synthesia', 'Runway', 'Filmora', 'OpusClip'],
            'Image Generation': ['GPT-4o', 'Midjourney'],
            'Meeting Assistants': ['Fathom', 'Nyota'],
            'Automation': ['n8n'],
            'Research': ['Deep Research', 'NotebookLM'],
            'Writing': ['Rytr', 'Sudowrite'],
            'Grammar': ['Grammarly', 'Wordtune'],
            'Search Engines': ['Perplexity', 'ChatGPT search'],
            'Social Media': ['Vista Social', 'FeedHive'],
            'Design': ['Canva Magic Studio', 'Looka'],
            'Development': ['Bubble', 'Bolt', 'Lovable', 'Cursor', 'v0'],
            'Project Management': ['Asana', 'ClickUp'],
            'Scheduling': ['Reclaim', 'Clockwise'],
            'Customer Service': ['Tidio AI', 'Hiver'],
            'Recruitment': ['Textio', 'CVViZ'],
            'Knowledge Management': ['Notion AI Q&A', 'Guru'],
            'Email': ['Hubspot Email Writer', 'SaneBox', 'Shortwave'],
            'Presentations': ['Gamma', 'Presentations.ai'],
            'Resume Builders': ['Teal', 'Kickresume'],
            'Voice Generation': ['ElevenLabs', 'Murf'],
            'Music Generation': ['Suno', 'Udio'],
            'Marketing': ['AdCreative'],
            'Sales': ['Clay'],
            'Legal': ['Harvey']
        };

        // Tool URLs mapping
        const toolUrls = {
            'ChatGPT': 'https://chatgpt.com/',
            'Claude': 'https://claude.ai/',
            'Gemini': 'https://gemini.google.com/',
            'DeepSeek': 'https://www.deepseek.com/',
            'Grok': 'https://x.com/i/grok',
            'Synthesia': 'https://www.synthesia.io/',
            'Runway': 'https://runwayml.com/',
            'Filmora': 'https://filmora.wondershare.com/',
            'OpusClip': 'https://www.opus.pro/',
            'Midjourney': 'https://www.midjourney.com/',
            'Fathom': 'https://www.fathom.video/',
            'Nyota': 'https://www.nyota.ai/',
            'n8n': 'https://n8n.io/',
            'NotebookLM': 'https://notebooklm.google/',
            'Rytr': 'https://rytr.me/',
            'Sudowrite': 'https://sudowrite.com/',
            'Grammarly': 'https://www.grammarly.com/',
            'Wordtune': 'https://www.wordtune.com/',
            'Perplexity': 'https://www.perplexity.ai/',
            'Vista Social': 'https://vistasocial.com/',
            'FeedHive': 'https://www.feedhive.com/',
            'Canva Magic Studio': 'https://www.canva.com/magic/',
            'Looka': 'https://looka.com/',
            'Bubble': 'https://bubble.io/',
            'Bolt': 'https://bolt.new/',
            'Lovable': 'https://lovable.dev/',
            'Cursor': 'https://www.cursor.com/',
            'v0': 'https://v0.dev/',
            'Asana': 'https://asana.com/',
            'ClickUp': 'https://clickup.com/',
            'Reclaim': 'https://reclaim.ai/',
            'Clockwise': 'https://www.getclockwise.com/',
            'Tidio AI': 'https://www.tidio.com/',
            'Hiver': 'https://hiverhq.com/',
            'Textio': 'https://textio.com/',
            'CVViZ': 'https://cvviz.com/',
            'Notion AI Q&A': 'https://www.notion.com/product/ai',
            'Guru': 'https://www.getguru.com/',
            'Hubspot Email Writer': 'https://www.hubspot.com/products/marketing/ai-email-writer',
            'SaneBox': 'https://www.sanebox.com/',
            'Shortwave': 'https://www.shortwave.com/',
            'Gamma': 'https://gamma.app/',
            'Presentations.ai': 'https://www.presentations.ai/',
            'Teal': 'https://www.tealhq.com/',
            'Kickresume': 'https://www.kickresume.com/',
            'ElevenLabs': 'https://elevenlabs.io/',
            'Murf': 'https://murf.ai/',
            'Suno': 'https://suno.com/',
            'Udio': 'https://www.udio.com/',
            'AdCreative': 'https://www.adcreative.ai/',
            'Clay': 'https://www.clay.com/',
            'Harvey': 'https://www.harvey.ai/'
        };



        // Tool image URLs mapping with attractive, high-quality images
        const toolImages = {
            'ChatGPT': 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Claude': 'https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Gemini': 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'DeepSeek': 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Grok': 'https://images.unsplash.com/photo-1516110833967-0b5716ca1387?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Synthesia': 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Runway': 'https://images.unsplash.com/photo-1536240478700-b869070f9279?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Filmora': 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'OpusClip': 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'GPT-4o': 'https://images.unsplash.com/photo-1676299081847-824916de030a?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Midjourney': 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Fathom': 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Nyota': 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'n8n': 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Deep Research': 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'NotebookLM': 'https://images.unsplash.com/photo-1456324504439-367cee3b3c32?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Rytr': 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Sudowrite': 'https://images.unsplash.com/photo-1471107340929-a87cd0f5b5f3?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Grammarly': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Wordtune': 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Perplexity': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'ChatGPT search': 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Vista Social': 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'FeedHive': 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Canva Magic Studio': 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Looka': 'https://images.unsplash.com/photo-1626785774573-4b799315345d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Bubble': 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Bolt': 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Lovable': 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Cursor': 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'v0': 'https://images.unsplash.com/photo-1587620962725-abab7fe55159?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Asana': 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'ClickUp': 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Reclaim': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Clockwise': 'https://images.unsplash.com/photo-1501139083538-0139583c060f?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Tidio AI': 'https://images.unsplash.com/photo-1553484771-371a605b060b?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Hiver': 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Textio': 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'CVViZ': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Notion AI Q&A': 'https://images.unsplash.com/photo-1484480974693-6ca0a78fb36b?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Guru': 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Hubspot Email Writer': 'https://images.unsplash.com/photo-1596526131083-e8c633c948d2?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'SaneBox': 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Shortwave': 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Gamma': 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Presentations.ai': 'https://images.unsplash.com/photo-1551434678-e076c223a692?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Teal': 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Kickresume': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'ElevenLabs': 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Murf': 'https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Suno': 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Udio': 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'AdCreative': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Clay': 'https://images.unsplash.com/photo-1553484771-371a605b060b?w=400&h=225&fit=crop&crop=center&auto=format&q=80',
            'Harvey': 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=225&fit=crop&crop=center&auto=format&q=80'
        };

        // Generate tool descriptions based on content
        const toolDescriptions = {
            'ChatGPT': 'The world\'s most popular AI chatbot with 200 million users, perfect for conversations and content creation.',
            'Claude': 'Advanced AI assistant by Anthropic, excellent for complex reasoning and analysis.',
            'Gemini': 'Google\'s powerful AI model with multimodal capabilities.',
            'DeepSeek': 'Advanced AI model for deep reasoning and problem-solving.',
            'Grok': 'X\'s AI assistant with real-time information access.',
            'Synthesia': 'Leading AI video generation platform with 230+ avatars in 140+ languages.',
            'Runway': 'Creative AI tools for video editing and generation.',
            'Filmora': 'User-friendly video editing software with AI features.',
            'OpusClip': 'AI-powered video clipping and editing tool.',
            'GPT-4o': 'OpenAI\'s most advanced multimodal AI model with vision and reasoning capabilities.',
            'Midjourney': 'Premium AI image generation with artistic capabilities.',
            'Fathom': 'AI meeting assistant for recording and transcription.',
            'Nyota': 'Smart meeting assistant with advanced features.',
            'n8n': 'Workflow automation platform with AI capabilities.',
            'Deep Research': 'Advanced AI-powered research tool for comprehensive analysis and insights.',
            'NotebookLM': 'Google\'s AI research and note-taking assistant.',
            'Rytr': 'AI writing assistant for various content types.',
            'Sudowrite': 'Creative writing AI specifically for authors.',
            'Grammarly': 'AI-powered grammar and writing improvement tool.',
            'Wordtune': 'AI writing companion for better communication.',
            'Perplexity': 'AI-powered search engine with real-time answers.',
            'ChatGPT search': 'OpenAI\'s integrated search functionality within ChatGPT for real-time information.',
            'Vista Social': 'Social media management with AI features.',
            'FeedHive': 'AI-driven social media scheduling and analytics.',
            'Canva Magic Studio': 'AI-powered design tools within Canva.',
            'Looka': 'AI logo and brand design platform.',
            'Bubble': 'No-code app builder with AI assistance.',
            'Bolt': 'AI-powered development environment.',
            'Lovable': 'AI-assisted app development platform.',
            'Cursor': 'AI-powered code editor.',
            'v0': 'AI interface design and development tool.',
            'Asana': 'Project management with AI-powered features.',
            'ClickUp': 'Comprehensive project management with AI tools.',
            'Reclaim': 'AI scheduling assistant for optimal time management.',
            'Clockwise': 'AI-powered calendar optimization for focus time.',
            'Tidio AI': 'Customer service platform with AI chatbot.',
            'Hiver': 'AI-powered customer support for shared inboxes.',
            'Textio': 'AI platform for inclusive recruitment communications.',
            'CVViZ': 'AI-powered recruitment and resume screening.',
            'Notion AI Q&A': 'AI assistant for Notion knowledge bases.',
            'Guru': 'AI-powered knowledge management platform.',
            'Hubspot Email Writer': 'AI email assistant for marketing campaigns.',
            'SaneBox': 'AI email management and organization.',
            'Shortwave': 'AI-powered email client with smart features.',
            'Gamma': 'AI presentation maker with beautiful designs.',
            'Presentations.ai': 'AI-powered presentation creation tool.',
            'Teal': 'AI resume builder and career platform.',
            'Kickresume': 'AI-powered resume and cover letter builder.',
            'ElevenLabs': 'Leading AI voice generation and cloning.',
            'Murf': 'AI voiceover and text-to-speech platform.',
            'Suno': 'AI music generation from text prompts.',
            'Udio': 'AI music creation with genre customization.',
            'AdCreative': 'AI-powered ad creative generation.',
            'Clay': 'AI lead generation and prospecting platform.',
            'Harvey': 'AI legal assistant for contract drafting and review.'
        };

        // Create AI tools array
        Object.entries(categories).forEach(([category, tools]) => {
            tools.forEach(tool => {
                aiTools.push({
                    id: aiTools.length + 1,
                    name: tool,
                    description: toolDescriptions[tool] || `AI-powered ${category.toLowerCase()} tool`,
                    category: category,
                    url: toolUrls[tool] || '#',
                    imageUrl: toolImages[tool] || 'https://i.imgur.com/placeholder.png',
                    pricing: 'Freemium', // Default pricing
                    featured: ['ChatGPT', 'Synthesia', 'Midjourney', 'Grammarly'].includes(tool)
                });
            });
        });

        // Ensure data directory exists
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir);
        }

        // Save to JSON file
        const jsonData = {
            lastUpdated: new Date().toISOString(),
            totalTools: aiTools.length,
            categories: Object.keys(categories),
            tools: aiTools
        };

        fs.writeFileSync(path.join(dataDir, 'ai-tools.json'), JSON.stringify(jsonData, null, 2));

        console.log(`Successfully scraped ${aiTools.length} AI tools!`);
        console.log(`Data saved to data/ai-tools.json`);

        return jsonData;

    } catch (error) {
        console.error('Error scraping AI tools:', error.message);
        throw error;
    }
}

// Run scraper if called directly
if (require.main === module) {
    scrapeAITools()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
}

module.exports = { scrapeAITools };
